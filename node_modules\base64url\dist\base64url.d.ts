/// <reference types="node" />
export interface Base64Url {
    (input: string | <PERSON>uff<PERSON>, encoding?: string): string;
    encode(input: string | <PERSON>uffer, encoding?: string): string;
    decode(base64url: string, encoding?: string): string;
    toBase64(base64url: string | Buffer): string;
    fromBase64(base64: string): string;
    toBuffer(base64url: string): Buffer;
}
declare let base64url: Base64Url;
export default base64url;
