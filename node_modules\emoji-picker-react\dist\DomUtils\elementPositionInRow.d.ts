import { NullableElement } from './selectors';
export declare function elementCountInRow(parent: NullableElement, element: NullableElement): number;
export declare function elementIndexInRow(parent: NullableElement, element: NullableElement): number;
export declare function rowNumber(parent: NullableElement, element: NullableElement): number;
export declare function hasNextRow(parent: NullableElement, element: NullableElement): boolean;
export declare function getElementInRow(elements: HTMLElement[], row: number, elementsInRow: number, indexInRow: number): NullableElement;
export declare function getElementInNextRow(allElements: HTMLElement[], currentRow: number, elementsInRow: number, index: number): NullableElement;
export declare function getElementInPrevRow(allElements: HTMLElement[], currentRow: number, elementsInRow: number, index: number): NullableElement;
export declare function firstVisibleElementInContainer(parent: NullableElement, elements: HTMLElement[], maxVisibilityDiffThreshold?: number): NullableElement;
export declare function hasNextElementSibling(element: HTMLElement): boolean;
