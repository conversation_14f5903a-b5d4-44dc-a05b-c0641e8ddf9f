{"name": "j<PERSON>y", "title": "j<PERSON><PERSON><PERSON>", "description": "JavaScript library for DOM operations", "version": "3.7.1", "main": "dist/jquery.js", "homepage": "https://jquery.com", "author": {"name": "OpenJS Foundation and other contributors", "url": "https://github.com/jquery/jquery/blob/3.7.1/AUTHORS.txt"}, "repository": {"type": "git", "url": "https://github.com/jquery/jquery.git"}, "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "license": "MIT", "devDependencies": {"@babel/core": "7.3.3", "@babel/plugin-transform-for-of": "7.2.0", "bootstrap": "5.3.0", "colors": "1.4.0", "commitplease": "3.2.0", "core-js": "2.6.5", "eslint-config-jquery": "3.0.0", "grunt": "1.5.3", "grunt-babel": "8.0.0", "grunt-cli": "1.4.3", "grunt-compare-size": "0.4.2", "grunt-contrib-uglify": "3.4.0", "grunt-contrib-watch": "1.1.0", "grunt-eslint": "22.0.0", "grunt-git-authors": "3.2.0", "grunt-jsonlint": "2.1.2", "grunt-karma": "4.0.2", "grunt-newer": "1.3.0", "grunt-npmcopy": "0.2.0", "gzip-js": "0.3.2", "husky": "4.2.5", "jsdom": "19.0.0", "karma": "6.4.1", "karma-browserstack-launcher": "1.6.0", "karma-chrome-launcher": "3.1.1", "karma-firefox-launcher": "2.1.2", "karma-ie-launcher": "1.0.0", "karma-jsdom-launcher": "12.0.0", "karma-qunit": "4.1.2", "karma-webkit-launcher": "2.1.0", "load-grunt-tasks": "5.1.0", "native-promise-only": "0.8.1", "playwright-webkit": "1.30.0", "promises-aplus-tests": "2.1.2", "q": "1.5.1", "qunit": "2.9.2", "raw-body": "2.3.3", "requirejs": "2.3.6", "sinon": "2.3.7", "strip-json-comments": "2.0.1", "testswarm": "1.1.2", "uglify-js": "3.4.7"}, "scripts": {"build": "npm install && npm run build-all-variants", "build-all-variants": "grunt custom:slim --filename=jquery.slim.js && grunt custom", "start": "grunt watch", "test:browserless": "grunt && npm run test:node_smoke_tests && grunt test:slow", "test:browser": "grunt && grunt karma:main", "test:amd": "grunt && grunt karma:amd", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main", "test:selector-native": "grunt test:prepare && grunt custom:-selector && grunt karma:main", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main", "test:node_smoke_tests:full": "grunt node_smoke_tests:./dist/jquery.js", "test:node_smoke_tests:slim": "grunt node_smoke_tests:./dist/jquery.slim.js", "test:node_smoke_tests": "npm run test:node_smoke_tests:full && npm run test:node_smoke_tests:slim", "test": "npm run test:browserless && npm run test:slim && npm run test:no-deprecated && npm run test:selector-native && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "jenkins": "npm run test:browserless"}, "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "husky": {"hooks": {"commit-msg": "commitplease .git/COMMIT_EDITMSG", "pre-commit": "grunt lint:newer qunit_fixture"}}}